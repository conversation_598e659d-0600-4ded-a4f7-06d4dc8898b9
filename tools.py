"""KYC verification tools implementation"""

from typing import Dict, Any
from mcp.types import Tool, TextContent
import json

from kyc_client import KYCClient
from config import ENDPOINTS


class KYCTools:
    """KYC verification tools"""
    
    def __init__(self):
        self.client = KYCClient()
    
    def get_tools(self) -> list[Tool]:
        """Get all available KYC tools"""
        return [
            # Document Verification Tools
            Tool(
                name="verify_tan",
                description="Verify TAN (Tax Deduction Account Number)",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "TAN number to verify"}
                    },
                    "required": ["id_number"]
                }
            ),
            Tool(
                name="verify_voter_id",
                description="Verify Voter ID",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "Voter ID number"},
                        "authorization_token": {"type": "string", "description": "Authorization token"},
                        "customer_id": {"type": "string", "description": "Customer ID"}
                    },
                    "required": ["id_number", "authorization_token", "customer_id"]
                }
            ),
            Tool(
                name="verify_driving_license",
                description="Verify Driving License",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "License number"},
                        "dob": {"type": "string", "description": "Date of birth (YYYY-MM-DD)"}
                    },
                    "required": ["id_number", "dob"]
                }
            ),
            Tool(
                name="verify_passport",
                description="Verify Passport details",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "Passport file number"},
                        "dob": {"type": "string", "description": "Date of birth (YYYY-MM-DD)"}
                    },
                    "required": ["id_number", "dob"]
                }
            ),
            Tool(
                name="generate_aadhaar_otp",
                description="Generate OTP for Aadhaar verification",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "Aadhaar number"},
                        "authorization_token": {"type": "string", "description": "Authorization token"}
                    },
                    "required": ["id_number", "authorization_token"]
                }
            ),
            Tool(
                name="validate_aadhaar",
                description="Validate Aadhaar number",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "Aadhaar number"},
                        "authorization_token": {"type": "string", "description": "Authorization token"}
                    },
                    "required": ["id_number", "authorization_token"]
                }
            ),
            # Bank Verification Tools
            Tool(
                name="verify_bank_account",
                description="Verify bank account details",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "Account number"},
                        "ifsc": {"type": "string", "description": "IFSC code"},
                        "ifsc_details": {"type": "boolean", "description": "Include IFSC details"},
                        "authorization_token": {"type": "string", "description": "Authorization token"}
                    },
                    "required": ["id_number", "ifsc", "authorization_token"]
                }
            ),
            Tool(
                name="verify_upi",
                description="Verify UPI ID",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "upi_id": {"type": "string", "description": "UPI ID to verify"},
                        "authorization_token": {"type": "string", "description": "Authorization token"}
                    },
                    "required": ["upi_id", "authorization_token"]
                }
            ),
            # Corporate Verification Tools
            Tool(
                name="verify_gstin",
                description="Verify GSTIN details",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "GSTIN number"},
                        "authorization_token": {"type": "string", "description": "Authorization token"}
                    },
                    "required": ["id_number", "authorization_token"]
                }
            ),
            Tool(
                name="verify_company_cin",
                description="Verify company details by CIN",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "id_number": {"type": "string", "description": "CIN number"},
                        "authorization_token": {"type": "string", "description": "Authorization token"}
                    },
                    "required": ["id_number", "authorization_token"]
                }
            ),
            # OCR Tools
            Tool(
                name="ocr_pan_card",
                description="Extract data from PAN card image using OCR",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to PAN card image file"}
                    },
                    "required": ["file_path"]
                }
            ),
            Tool(
                name="ocr_passport",
                description="Extract data from passport image using OCR",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to passport image file"}
                    },
                    "required": ["file_path"]
                }
            ),
            # Face Verification Tools
            Tool(
                name="face_match",
                description="Match face between selfie and ID card",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "selfie_path": {"type": "string", "description": "Path to selfie image"},
                        "id_card_path": {"type": "string", "description": "Path to ID card image"}
                    },
                    "required": ["selfie_path", "id_card_path"]
                }
            ),
            Tool(
                name="face_liveness",
                description="Check face liveness in image",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to face image file"}
                    },
                    "required": ["file_path"]
                }
            )
        ]

    async def execute_tool(self, name: str, arguments: Dict[str, Any]) -> list[TextContent]:
        """Execute a KYC tool"""
        try:
            if name == "verify_tan":
                return await self._verify_tan(arguments)
            elif name == "verify_voter_id":
                return await self._verify_voter_id(arguments)
            elif name == "verify_driving_license":
                return await self._verify_driving_license(arguments)
            elif name == "verify_passport":
                return await self._verify_passport(arguments)
            elif name == "generate_aadhaar_otp":
                return await self._generate_aadhaar_otp(arguments)
            elif name == "validate_aadhaar":
                return await self._validate_aadhaar(arguments)
            elif name == "verify_bank_account":
                return await self._verify_bank_account(arguments)
            elif name == "verify_upi":
                return await self._verify_upi(arguments)
            elif name == "verify_gstin":
                return await self._verify_gstin(arguments)
            elif name == "verify_company_cin":
                return await self._verify_company_cin(arguments)
            elif name == "ocr_pan_card":
                return await self._ocr_pan_card(arguments)
            elif name == "ocr_passport":
                return await self._ocr_passport(arguments)
            elif name == "face_match":
                return await self._face_match(arguments)
            elif name == "face_liveness":
                return await self._face_liveness(arguments)
            else:
                return [TextContent(type="text", text=f"Unknown tool: {name}")]
        except Exception as e:
            return [TextContent(type="text", text=f"Error executing {name}: {str(e)}")]

    async def _verify_tan(self, args: Dict[str, Any]) -> list[TextContent]:
        """Verify TAN number"""
        data = {"id_number": args["id_number"]}
        response = await self.client.post_json(ENDPOINTS["tan"], data)
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _verify_voter_id(self, args: Dict[str, Any]) -> list[TextContent]:
        """Verify Voter ID"""
        data = {"id_number": args["id_number"]}
        response = await self.client.post_json(
            ENDPOINTS["voter_id"],
            data,
            authorization_token=args["authorization_token"],
            customer_id=args["customer_id"]
        )
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _verify_driving_license(self, args: Dict[str, Any]) -> list[TextContent]:
        """Verify Driving License"""
        data = {
            "id_number": args["id_number"],
            "dob": args["dob"]
        }
        response = await self.client.post_json(ENDPOINTS["driving_license"], data)
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _verify_passport(self, args: Dict[str, Any]) -> list[TextContent]:
        """Verify Passport"""
        data = {
            "id_number": args["id_number"],
            "dob": args["dob"]
        }
        response = await self.client.post_json(ENDPOINTS["passport"], data)
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _generate_aadhaar_otp(self, args: Dict[str, Any]) -> list[TextContent]:
        """Generate Aadhaar OTP"""
        data = {"id_number": args["id_number"]}
        response = await self.client.post_json(
            ENDPOINTS["aadhaar_generate_otp"],
            data,
            authorization_token=args["authorization_token"]
        )
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _validate_aadhaar(self, args: Dict[str, Any]) -> list[TextContent]:
        """Validate Aadhaar"""
        data = {"id_number": args["id_number"]}
        response = await self.client.post_json(
            ENDPOINTS["aadhaar_validation"],
            data,
            authorization_token=args["authorization_token"]
        )
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _verify_bank_account(self, args: Dict[str, Any]) -> list[TextContent]:
        """Verify Bank Account"""
        data = {
            "id_number": args["id_number"],
            "ifsc": args["ifsc"],
            "ifsc_details": args.get("ifsc_details", True)
        }
        response = await self.client.post_json(
            ENDPOINTS["bank_verification"],
            data,
            authorization_token=args["authorization_token"]
        )
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _verify_upi(self, args: Dict[str, Any]) -> list[TextContent]:
        """Verify UPI ID"""
        data = {"upi_id": args["upi_id"]}
        response = await self.client.post_json(
            ENDPOINTS["upi_verification"],
            data,
            authorization_token=args["authorization_token"]
        )
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _verify_gstin(self, args: Dict[str, Any]) -> list[TextContent]:
        """Verify GSTIN"""
        data = {"id_number": args["id_number"]}
        response = await self.client.post_json(
            ENDPOINTS["gstin"],
            data,
            authorization_token=args["authorization_token"]
        )
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _verify_company_cin(self, args: Dict[str, Any]) -> list[TextContent]:
        """Verify Company CIN"""
        data = {"id_number": args["id_number"]}
        response = await self.client.post_json(
            ENDPOINTS["company_details"],
            data,
            authorization_token=args["authorization_token"]
        )
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _ocr_pan_card(self, args: Dict[str, Any]) -> list[TextContent]:
        """OCR PAN Card"""
        files = {"file": args["file_path"]}
        response = await self.client.post_form(ENDPOINTS["ocr_pan"], files)
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _ocr_passport(self, args: Dict[str, Any]) -> list[TextContent]:
        """OCR Passport"""
        files = {"file": args["file_path"]}
        response = await self.client.post_form(ENDPOINTS["ocr_passport"], files)
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _face_match(self, args: Dict[str, Any]) -> list[TextContent]:
        """Face Match"""
        files = {
            "selfie": args["selfie_path"],
            "id_card": args["id_card_path"]
        }
        response = await self.client.post_form(ENDPOINTS["face_match"], files)
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]

    async def _face_liveness(self, args: Dict[str, Any]) -> list[TextContent]:
        """Face Liveness"""
        files = {"file": args["file_path"]}
        response = await self.client.post_form(ENDPOINTS["face_liveness"], files)
        return [TextContent(type="text", text=json.dumps(response.model_dump(), indent=2))]
