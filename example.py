#!/usr/bin/env python3
"""Example usage of KYC MCP Server tools"""

import asyncio
import json
import os
from tools import KYCTools


async def example_tan_verification():
    """Example: Verify a TAN number (no auth required)"""
    print("=== TAN Verification Example ===")
    
    kyc_tools = KYCTools()
    
    # Example TAN verification
    args = {
        "id_number": "RTKT06731E"  # Example TAN from the API docs
    }
    
    try:
        result = await kyc_tools.execute_tool("verify_tan", args)
        print("TAN Verification Result:")
        print(result[0].text)
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n")


async def example_with_auth():
    """Example with authentication (requires API token)"""
    print("=== Example with Authentication ===")
    
    # Check if .env file has been configured
    if not os.path.exists('.env'):
        print("⚠️  .env file not found. Please create it from .env.template")
        return
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        token = os.getenv('SUREPASS_API_TOKEN')
        customer_id = os.getenv('SUREPASS_CUSTOMER_ID')
        
        if not token or token == 'your_bearer_token_here':
            print("⚠️  Please configure your API credentials in .env file")
            print("   Edit .env and replace 'your_bearer_token_here' with your actual token")
            return
        
        kyc_tools = KYCTools()
        
        # Example Aadhaar validation (requires auth)
        args = {
            "id_number": "123456789012",  # Example Aadhaar number
            "authorization_token": f"Bearer {token}"
        }
        
        print("Testing Aadhaar validation with your API credentials...")
        result = await kyc_tools.execute_tool("validate_aadhaar", args)
        print("Aadhaar Validation Result:")
        print(result[0].text)
        
    except ImportError:
        print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n")


async def list_all_tools():
    """List all available tools"""
    print("=== Available KYC Tools ===")
    
    kyc_tools = KYCTools()
    tools = kyc_tools.get_tools()
    
    print(f"Total tools available: {len(tools)}\n")
    
    for i, tool in enumerate(tools, 1):
        print(f"{i:2d}. {tool.name}")
        print(f"    Description: {tool.description}")
        
        # Show required parameters
        required = tool.inputSchema.get('required', [])
        if required:
            print(f"    Required: {', '.join(required)}")
        
        print()


async def main():
    """Run examples"""
    print("KYC MCP Server - Example Usage")
    print("=" * 50)
    
    # List all available tools
    await list_all_tools()
    
    # Test TAN verification (no auth required)
    await example_tan_verification()
    
    # Test with authentication (requires API credentials)
    await example_with_auth()
    
    print("=" * 50)
    print("How to use with real API credentials:")
    print("1. Get SurePass API credentials from https://surepass.io")
    print("2. Edit .env file and replace placeholder values:")
    print("   SUREPASS_API_TOKEN=your_actual_bearer_token")
    print("   SUREPASS_CUSTOMER_ID=your_actual_customer_id")
    print("3. Run this script again to test with real API calls")
    print("\nTo run the MCP server:")
    print("   python kyc_mcp_server.py")


if __name__ == "__main__":
    asyncio.run(main())
