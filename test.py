#!/usr/bin/env python3
"""Simple test script for KYC MCP Server"""

import asyncio
import json


async def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from tools import KYCTools
        from kyc_client import KYCClient
        from models import KYCResponse
        from config import BASE_URL, ENDPOINTS
        print("✓ All modules imported successfully")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False


async def test_tools_list():
    """Test that tools can be listed"""
    print("Testing tools listing...")
    
    try:
        from tools import KYCTools
        kyc_tools = KYCTools()
        tools = kyc_tools.get_tools()
        
        print(f"✓ Found {len(tools)} available tools:")
        for i, tool in enumerate(tools[:5]):  # Show first 5 tools
            print(f"  {i+1}. {tool.name}: {tool.description}")
        
        if len(tools) > 5:
            print(f"  ... and {len(tools) - 5} more tools")
        
        return True
    except Exception as e:
        print(f"✗ Failed to list tools: {e}")
        return False


async def test_tool_execution():
    """Test tool execution (without actual API call)"""
    print("Testing tool execution...")
    
    try:
        from tools import KYCTools
        kyc_tools = KYCTools()
        
        # Test with invalid tool name to check error handling
        result = await kyc_tools.execute_tool("invalid_tool", {})
        
        if result and len(result) > 0 and "Unknown tool" in result[0].text:
            print("✓ Tool execution error handling works correctly")
            return True
        else:
            print("✗ Tool execution error handling not working as expected")
            return False
    except Exception as e:
        print(f"✗ Failed to test tool execution: {e}")
        return False


async def test_config():
    """Test configuration"""
    print("Testing configuration...")
    
    try:
        from config import BASE_URL, ENDPOINTS
        print(f"✓ Base URL: {BASE_URL}")
        print(f"✓ Available endpoints: {len(ENDPOINTS)}")
        
        # Show some example endpoints
        example_endpoints = list(ENDPOINTS.items())[:3]
        for name, path in example_endpoints:
            print(f"  - {name}: {path}")
        
        return True
    except Exception as e:
        print(f"✗ Failed to test config: {e}")
        return False


async def main():
    """Run all tests"""
    print("KYC MCP Server - Quick Test")
    print("=" * 40)
    
    tests = [
        ("Module Imports", test_imports),
        ("Tools Listing", test_tools_list),
        ("Tool Execution", test_tool_execution),
        ("Configuration", test_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The KYC MCP Server is ready to use.")
        print("\nNext steps:")
        print("1. Copy .env.template to .env")
        print("2. Add your SurePass API credentials to .env")
        print("3. Run: python kyc_mcp_server.py")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
